#coding:gbk


# 导入常用数据处理和技术分析库
import pandas as pd  # 数据处理库，用于处理时间序列数据
import numpy as np   # 数值计算库，用于数学运算和统计计算
import talib         # 技术分析库，提供各种技术指标计算函数

def init(C):

    # 构建完整的股票代码，格式：股票代码.市场代码（如：000001.SZ）
    C.stock = C.stockcode + '.' + C.market

    # 设置回测账户ID，回测模式下可以使用任意字符串作为账户标识
    C.accountid = "testS"

# 策略配置参数===============================================================================

    # 计算指标的k线数量 
    indicator_bars = 50
    tick_count = indicator_bars * 2

    # 下载指定数量的tick数据
    

    # 获取所需要的tick数据
    hist_data = C.get_market_data_ex(
        [],  # 获取所有字段（QMT会自动处理tick支持的字段）
        [C.stock],
        period=C.period,
        count=tick_count,  # 只获取需要的数量
        dividend_type='none'  # tick数据不需要复权
    )
    # 提取 lastPrice和volume 
    price = hist_data[C.stock][['lastPrice', 'volume']]
    #print(price.tail())# 行情数据查看
    #volume = hist_data['volume']
    #print(f"价格: {price}, 成交量: {volume}")
    #print(hist_data[C.stock].columns)   #列名

    # df是的DataFrame，包含'lastPrice'和'volume'列
    # 重置索引以获取连续的整数索引
    df_reset = price.reset_index()

    # 将累积成交量转换为增量成交量
    df_reset['volume'] = df_reset['volume'].diff().fillna(0)
    # 创建一个分组索引，每两个数据点为一组
    group_index = df_reset.index // 2

    #print(df_reset)
    # 使用groupby和agg方法进行聚合
    merged_df = df_reset.groupby(group_index).agg({
        'stime': 'first',           # 时间取每组第一个
        'lastPrice': ['first', 'max', 'min', 'last'],  # 生成Open, High, Low, Close
        'volume': 'sum'             # 成交量求和
    })

    # 展平列名
    merged_df.columns = ['stime', 'Open', 'High', 'Low', 'Close', 'Volume']


    merged_df['stime'] = pd.to_datetime(merged_df['stime'], format='%Y%m%d%H%M%S.%f')
    # 将stime重新设置为索引
    merged_df.set_index('stime', inplace=True)
    print(len(merged_df))
    print(merged_df.tail())   # 显示部分
    #print(merged_df)          # 显示全部

def handlebar(C):
    # === 第一步：获取当前K线时间信息 ===
    #return
    # 获取当前K线的时间戳并转换为可读格式
    # C.barpos: 当前K线在数据序列中的位置索引
    # C.get_bar_timetag(): 获取指定位置K线的时间戳
    # timetag_to_datetime(): 将时间戳转换为日期时间字符串
    bar_date = timetag_to_datetime(C.get_bar_timetag(C.barpos), '%Y%m%d%H%M%S')
    print(bar_date)


    # 获取所需要的tick数据
    hist_data = C.get_market_data_ex(
        [],  # 获取所有字段（QMT会自动处理tick支持的字段）
        [C.stock],
        period=C.period,
        end_time=bar_date,
        count=1,  # 回测中取每次最新的1个数据
        dividend_type='none'  # tick数据不需要复权
        )
    # 提取 lastPrice和volume 
    price = hist_data[C.stock][['lastPrice', 'volume']]
    #print(price)
    #print('========================================')
	# 每两个数据合成一个并添加到merged_df中
    